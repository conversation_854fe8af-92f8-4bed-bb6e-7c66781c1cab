<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Import Google Fonts for professional typography */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        /* Root variables for consistent theming */
        :root {
            --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3730a3 25%, #5b21b6 50%, #7c2d12 75%, #991b1b 100%);
            --glass-bg: rgba(255, 255, 255, 0.98);
            --glass-border: rgba(255, 255, 255, 0.3);
            --shadow-primary: 0 32px 64px rgba(0, 0, 0, 0.12);
            --shadow-hover: 0 20px 40px rgba(59, 130, 246, 0.4);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --focus-ring: rgba(59, 130, 246, 0.15);
        }

        /* Global styles */
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Enhanced gradient background with mesh pattern */
        .gradient-bg {
            background: var(--primary-gradient);
            min-height: 100vh;
            position: relative;
        }

        .gradient-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Premium glass morphism effect */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(24px) saturate(180%);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-primary);
            position: relative;
            overflow: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        /* Sophisticated floating animation */
        .floating {
            animation: floating 4s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            25% {
                transform: translateY(-5px) rotate(0.5deg);
            }

            50% {
                transform: translateY(-8px) rotate(0deg);
            }

            75% {
                transform: translateY(-3px) rotate(-0.5deg);
            }
        }

        /* Advanced input styling */
        .input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .input-group label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: block;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }

        .input-group input {
            width: 100%;
            padding: 0.875rem 1rem 0.875rem 3rem;
            border: 1.5px solid var(--border-color);
            border-radius: 0.75rem;
            font-size: 0.9375rem;
            font-weight: 400;
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(8px);
        }

        .input-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px var(--focus-ring);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-1px);
        }

        .input-group input:focus+.input-icon {
            color: #3b82f6;
            transform: scale(1.1) translateY(-50%);
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 1rem;
        }

        /* Premium button styling */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
            border: none;
            color: white;
            font-weight: 600;
            font-size: 0.9375rem;
            letter-spacing: 0.025em;
            padding: 0.875rem 1.5rem;
            border-radius: 0.75rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e3a8a 100%);
        }

        .btn-primary:active {
            transform: translateY(0px);
        }

        /* Enhanced logo animation */
        .logo-pulse {
            animation: logoPulse 3s ease-in-out infinite;
            position: relative;
        }

        .logo-pulse::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6);
            border-radius: 50%;
            z-index: -1;
            animation: rotate 4s linear infinite;
            opacity: 0.7;
        }

        @keyframes logoPulse {

            0%,
            100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            }

            50% {
                transform: scale(1.05);
                box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Enhanced particle background effect */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 50%;
            animation: float 8s infinite linear;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
        }

        .particle:nth-child(odd) {
            background: rgba(59, 130, 246, 0.1);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(0);
                opacity: 0;
            }

            10% {
                opacity: 1;
                transform: translateY(90vh) rotate(36deg) scale(1);
            }

            90% {
                opacity: 1;
                transform: translateY(10vh) rotate(324deg) scale(1);
            }

            100% {
                transform: translateY(-10vh) rotate(360deg) scale(0);
                opacity: 0;
            }
        }

        /* Premium checkbox styling */
        .custom-checkbox {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(8px);
        }

        .custom-checkbox:hover {
            border-color: #3b82f6;
            transform: scale(1.05);
        }

        .custom-checkbox:checked {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: #3b82f6;
            transform: scale(1.05);
        }

        .custom-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(1);
            color: white;
            font-size: 12px;
            font-weight: 700;
            animation: checkmark 0.3s ease-in-out;
        }

        @keyframes checkmark {
            0% {
                transform: translate(-50%, -50%) scale(0) rotate(45deg);
            }

            50% {
                transform: translate(-50%, -50%) scale(1.2) rotate(45deg);
            }

            100% {
                transform: translate(-50%, -50%) scale(1) rotate(0deg);
            }
        }

        /* Enhanced social buttons */
        .social-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1rem;
            border: 1.5px solid var(--border-color);
            border-radius: 0.75rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(8px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            font-size: 0.875rem;
            color: var(--text-primary);
            position: relative;
            overflow: hidden;
        }

        .social-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .social-btn:hover::before {
            left: 100%;
        }

        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
            background: rgba(255, 255, 255, 0.95);
        }

        .social-btn i {
            margin-right: 0.5rem;
            font-size: 1.125rem;
        }

        /* Enhanced divider */
        .divider {
            position: relative;
            margin: 2rem 0 1.5rem;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--border-color), transparent);
        }

        .divider span {
            background: var(--glass-bg);
            padding: 0 1rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        /* Enhanced loading overlay */
        .loading-overlay {
            backdrop-filter: blur(8px);
            background: rgba(0, 0, 0, 0.6);
        }

        .loading-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .loading-spinner {
            width: 2rem;
            height: 2rem;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Responsive enhancements */
        @media (max-width: 640px) {
            .glass-card {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }

            .input-group input {
                padding: 0.75rem 0.875rem 0.75rem 2.75rem;
            }

            .input-icon {
                left: 0.875rem;
            }
        }
    </style>
</head>

<body class="gradient-bg flex items-center justify-center min-h-screen p-4 relative overflow-hidden">
    <!-- Animated particles background -->
    <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s; width: 4px; height: 4px;"></div>
        <div class="particle" style="left: 20%; animation-delay: 1s; width: 6px; height: 6px;"></div>
        <div class="particle" style="left: 30%; animation-delay: 2s; width: 3px; height: 3px;"></div>
        <div class="particle" style="left: 40%; animation-delay: 3s; width: 5px; height: 5px;"></div>
        <div class="particle" style="left: 50%; animation-delay: 4s; width: 4px; height: 4px;"></div>
        <div class="particle" style="left: 60%; animation-delay: 5s; width: 6px; height: 6px;"></div>
        <div class="particle" style="left: 70%; animation-delay: 6s; width: 3px; height: 3px;"></div>
        <div class="particle" style="left: 80%; animation-delay: 7s; width: 5px; height: 5px;"></div>
        <div class="particle" style="left: 90%; animation-delay: 8s; width: 4px; height: 4px;"></div>
    </div>

    <!-- Main login container -->
    <div class="w-full max-w-lg relative z-10">
        <!-- Login card -->
        <div class="glass-card rounded-3xl p-10 floating">
            <!-- Logo and title -->
            <div class="text-center mb-10">
                <div
                    class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-6 logo-pulse">
                    <i class="fas fa-folder-open text-white text-3xl"></i>
                </div>
                <h1 class="text-4xl font-bold text-gray-800 mb-3 tracking-tight">Welcome Back</h1>
                <p class="text-gray-600 text-lg font-medium">Sign in to your Document Management System</p>
            </div>

            <!-- Login form -->
            <form id="loginForm" class="space-y-0">
                <!-- Username field -->
                <div class="input-group">
                    <label for="username">Username or Email</label>
                    <div class="relative">
                        <input id="username" type="text" placeholder="Enter your username or email" required
                            autocomplete="username">
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>

                <!-- Password field -->
                <div class="input-group">
                    <label for="password">Password</label>
                    <div class="relative">
                        <input id="password" type="password" placeholder="Enter your password" required
                            autocomplete="current-password" style="padding-right: 3rem;">
                        <i class="fas fa-lock input-icon"></i>
                        <button type="button" id="togglePassword"
                            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-500 transition-colors focus:outline-none">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Remember me and forgot password -->
                <div class="flex items-center justify-between mb-8">
                    <label class="flex items-center cursor-pointer group">
                        <input type="checkbox" id="rememberMe" class="custom-checkbox mr-3">
                        <span
                            class="text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors">Remember
                            me</span>
                    </label>
                    <a href="#"
                        class="text-sm text-blue-600 hover:text-blue-800 transition-colors font-semibold hover:underline">
                        Forgot Password?
                    </a>
                </div>

                <!-- Login button -->
                <button type="submit" class="w-full btn-primary mb-6">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </button>
            </form>

            <!-- Divider -->
            <div class="divider">
                <div class="flex justify-center">
                    <span>Or continue with</span>
                </div>
            </div>

            <!-- Social login buttons -->
            <div class="grid grid-cols-2 gap-4 mb-8">
                <button type="button" class="social-btn">
                    <i class="fab fa-google text-red-500"></i>
                    <span>Google</span>
                </button>
                <button type="button" class="social-btn">
                    <i class="fab fa-microsoft text-blue-500"></i>
                    <span>Microsoft</span>
                </button>
            </div>

            <!-- Sign up link -->
            <div class="text-center">
                <p class="text-sm text-gray-600 font-medium">
                    Don't have an account?
                    <a href="#"
                        class="text-blue-600 hover:text-blue-800 font-semibold transition-colors hover:underline ml-1">
                        Sign up here
                    </a>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-10">
            <p class="text-white text-sm opacity-90 font-medium">
                &copy; 2025 DMS Corp. All rights reserved.
            </p>
            <div class="flex justify-center space-x-6 mt-4 text-white opacity-70">
                <a href="#" class="hover:opacity-100 transition-opacity text-sm">Privacy Policy</a>
                <a href="#" class="hover:opacity-100 transition-opacity text-sm">Terms of Service</a>
                <a href="#" class="hover:opacity-100 transition-opacity text-sm">Support</a>
            </div>
        </div>
    </div>

    <!-- Enhanced loading overlay -->
    <div id="loadingOverlay" class="hidden fixed inset-0 loading-overlay flex items-center justify-center z-50">
        <div class="loading-content flex items-center space-x-4">
            <div class="loading-spinner"></div>
            <div class="text-center">
                <div class="text-gray-800 font-semibold text-lg mb-1">Signing you in...</div>
                <div class="text-gray-600 text-sm">Please wait a moment</div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced login functionality
        class LoginManager {
            constructor() {
                this.initializeEventListeners();
                this.initializeParticles();
                this.initializeFormValidation();
            }

            initializeEventListeners() {
                // Toggle password visibility
                document.getElementById('togglePassword').addEventListener('click', this.togglePasswordVisibility.bind(this));

                // Form submission
                document.getElementById('loginForm').addEventListener('submit', this.handleFormSubmission.bind(this));

                // Input focus effects
                this.setupInputEffects();

                // Social login buttons
                this.setupSocialLogin();
            }

            togglePasswordVisibility() {
                const passwordInput = document.getElementById('password');
                const icon = document.querySelector('#togglePassword i');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            setupInputEffects() {
                const inputs = document.querySelectorAll('.input-group input');
                inputs.forEach(input => {
                    input.addEventListener('focus', () => {
                        input.parentElement.parentElement.classList.add('focused');
                    });

                    input.addEventListener('blur', () => {
                        input.parentElement.parentElement.classList.remove('focused');
                    });
                });
            }

            setupSocialLogin() {
                const socialButtons = document.querySelectorAll('.social-btn');
                socialButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        const provider = button.querySelector('span').textContent;
                        this.handleSocialLogin(provider);
                    });
                });
            }

            handleSocialLogin(provider) {
                console.log(`Initiating ${provider} login...`);
                // Implement social login logic here
                this.showNotification(`${provider} login coming soon!`, 'info');
            }

            initializeFormValidation() {
                const form = document.getElementById('loginForm');
                const inputs = form.querySelectorAll('input[required]');

                inputs.forEach(input => {
                    input.addEventListener('blur', () => this.validateField(input));
                    input.addEventListener('input', () => this.clearFieldError(input));
                });
            }

            validateField(field) {
                const value = field.value.trim();
                const fieldName = field.getAttribute('id');

                if (!value) {
                    this.showFieldError(field, `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
                    return false;
                }

                if (fieldName === 'username' && value.includes('@') && !this.isValidEmail(value)) {
                    this.showFieldError(field, 'Please enter a valid email address');
                    return false;
                }

                if (fieldName === 'password' && value.length < 6) {
                    this.showFieldError(field, 'Password must be at least 6 characters');
                    return false;
                }

                this.clearFieldError(field);
                return true;
            }

            isValidEmail(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }

            showFieldError(field, message) {
                this.clearFieldError(field);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error text-red-500 text-xs mt-1 font-medium';
                errorDiv.textContent = message;
                field.parentElement.parentElement.appendChild(errorDiv);
                field.classList.add('border-red-300');
            }

            clearFieldError(field) {
                const errorDiv = field.parentElement.parentElement.querySelector('.field-error');
                if (errorDiv) {
                    errorDiv.remove();
                }
                field.classList.remove('border-red-300');
            }

            async handleFormSubmission(e) {
                e.preventDefault();

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('rememberMe').checked;

                // Validate all fields
                const isValid = this.validateField(document.getElementById('username')) &&
                    this.validateField(document.getElementById('password'));

                if (!isValid) {
                    this.showNotification('Please fix the errors above', 'error');
                    return;
                }

                // Show loading overlay
                this.showLoadingOverlay();

                try {
                    // Simulate API call (replace with actual authentication)
                    await this.simulateLogin(username, password, rememberMe);

                    // Success - redirect to main page
                    this.showNotification('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);

                } catch (error) {
                    this.hideLoadingOverlay();
                    this.showNotification(error.message || 'Login failed. Please try again.', 'error');
                }
            }

            async simulateLogin(username, password, rememberMe) {
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        // Simple demo validation
                        if (username && password) {
                            if (rememberMe) {
                                localStorage.setItem('rememberMe', 'true');
                            }
                            resolve({ success: true });
                        } else {
                            reject(new Error('Invalid credentials'));
                        }
                    }, 2000);
                });
            }

            showLoadingOverlay() {
                document.getElementById('loadingOverlay').classList.remove('hidden');
            }

            hideLoadingOverlay() {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }

            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

                const colors = {
                    success: 'bg-green-500 text-white',
                    error: 'bg-red-500 text-white',
                    info: 'bg-blue-500 text-white'
                };

                notification.className += ` ${colors[type]}`;
                notification.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
                        <span class="font-medium">${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Remove after 4 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 4000);
            }

            initializeParticles() {
                const particles = document.querySelectorAll('.particle');
                particles.forEach((particle, index) => {
                    particle.style.animationDelay = Math.random() * 8 + 's';
                    particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
                });
            }
        }

        // Initialize the login manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new LoginManager();
        });
    </script>
</body>

</html>