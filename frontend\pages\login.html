<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Custom gradient background */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* Glass morphism effect */
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
        }

        /* Floating animation */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        /* Input focus effects */
        .input-group {
            position: relative;
        }

        .input-group input:focus+.input-icon {
            color: #667eea;
            transform: scale(1.1);
        }

        .input-group input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Button hover effects */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        /* Logo animation */
        .logo-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.8;
            }
        }

        /* Particle background effect */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Remember me checkbox styling */
        .custom-checkbox {
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .custom-checkbox:checked {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
        }

        .custom-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>

<body class="gradient-bg flex items-center justify-center min-h-screen p-4 relative overflow-hidden">
    <!-- Animated particles background -->
    <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s; width: 4px; height: 4px;"></div>
        <div class="particle" style="left: 20%; animation-delay: 1s; width: 6px; height: 6px;"></div>
        <div class="particle" style="left: 30%; animation-delay: 2s; width: 3px; height: 3px;"></div>
        <div class="particle" style="left: 40%; animation-delay: 3s; width: 5px; height: 5px;"></div>
        <div class="particle" style="left: 50%; animation-delay: 4s; width: 4px; height: 4px;"></div>
        <div class="particle" style="left: 60%; animation-delay: 5s; width: 6px; height: 6px;"></div>
        <div class="particle" style="left: 70%; animation-delay: 6s; width: 3px; height: 3px;"></div>
        <div class="particle" style="left: 80%; animation-delay: 7s; width: 5px; height: 5px;"></div>
        <div class="particle" style="left: 90%; animation-delay: 8s; width: 4px; height: 4px;"></div>
    </div>

    <!-- Main login container -->
    <div class="w-full max-w-md relative z-10">
        <!-- Login card -->
        <div class="glass-card rounded-2xl p-8 floating">
            <!-- Logo and title -->
            <div class="text-center mb-8">
                <div
                    class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4 logo-pulse">
                    <i class="fas fa-folder-open text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Welcome Back</h1>
                <p class="text-gray-600">Sign in to your Document Management System</p>
            </div>

            <!-- Login form -->
            <form id="loginForm" class="space-y-6">
                <!-- Username field -->
                <div class="input-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2" for="username">
                        Username or Email
                    </label>
                    <div class="relative">
                        <input
                            class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                            id="username" type="text" placeholder="Enter your username or email" required>
                        <i
                            class="fas fa-user input-icon absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 transition-all duration-300"></i>
                    </div>
                </div>

                <!-- Password field -->
                <div class="input-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2" for="password">
                        Password
                    </label>
                    <div class="relative">
                        <input
                            class="w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                            id="password" type="password" placeholder="Enter your password" required>
                        <i
                            class="fas fa-lock input-icon absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 transition-all duration-300"></i>
                        <button type="button" id="togglePassword"
                            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Remember me and forgot password -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="rememberMe" class="custom-checkbox mr-3">
                        <span class="text-sm text-gray-600">Remember me</span>
                    </label>
                    <a href="#" class="text-sm text-blue-600 hover:text-blue-800 transition-colors font-medium">
                        Forgot Password?
                    </a>
                </div>

                <!-- Login button -->
                <button type="submit"
                    class="w-full btn-primary text-white font-semibold py-3 px-6 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </button>
            </form>

            <!-- Divider -->
            <div class="mt-8 mb-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-4 bg-white text-gray-500">Or continue with</span>
                    </div>
                </div>
            </div>

            <!-- Social login buttons -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <button
                    class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fab fa-google text-red-500 mr-2"></i>
                    <span class="text-sm font-medium text-gray-700">Google</span>
                </button>
                <button
                    class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fab fa-microsoft text-blue-500 mr-2"></i>
                    <span class="text-sm font-medium text-gray-700">Microsoft</span>
                </button>
            </div>

            <!-- Sign up link -->
            <div class="text-center">
                <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a href="#" class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                        Sign up here
                    </a>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-white text-sm opacity-80">
                &copy; 2025 DMS Corp. All rights reserved.
            </p>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="text-gray-700 font-medium">Signing you in...</span>
        </div>
    </div>

    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function () {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Form submission
        document.getElementById('loginForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // Show loading overlay
            document.getElementById('loadingOverlay').classList.remove('hidden');

            // Simulate login process (replace with actual authentication)
            setTimeout(() => {
                // Hide loading overlay
                document.getElementById('loadingOverlay').classList.add('hidden');

                // For demo purposes, redirect to main page
                // In real implementation, validate credentials first
                if (username && password) {
                    window.location.href = 'index.html';
                } else {
                    alert('Please enter both username and password');
                }
            }, 1500);
        });

        // Add floating animation to particles with random delays
        document.addEventListener('DOMContentLoaded', function () {
            const particles = document.querySelectorAll('.particle');
            particles.forEach((particle, index) => {
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
            });
        });
    </script>
</body>

</html>