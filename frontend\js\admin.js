document.addEventListener("DOMContentLoaded", () => {
  // Admin Panel State Management
  let users = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "user",
      status: "active",
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "user",
      status: "inactive",
    },
    {
      id: 4,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "moderator",
      status: "active",
    },
  ];

  let permissions = [
    {
      id: 1,
      folder: "Documents",
      user: "<PERSON>",
      permission: "read",
      type: "user",
    },
    {
      id: 2,
      folder: "Documents/Confidential",
      user: "Admin Group",
      permission: "admin",
      type: "group",
    },
    {
      id: 3,
      folder: "Images",
      user: "<PERSON>",
      permission: "write",
      type: "user",
    },
    {
      id: 4,
      folder: "Videos",
      user: "All Users",
      permission: "read",
      type: "group",
    },
    {
      id: 5,
      folder: "Archive",
      user: "<PERSON>",
      permission: "none",
      type: "user",
    },
    {
      id: 6,
      folder: "Projects",
      user: "<PERSON>",
      permission: "admin",
      type: "user",
    },
    {
      id: 7,
      folder: "Projects/Marketing",
      user: "Marketing Team",
      permission: "write",
      type: "group",
    },
    {
      id: 8,
      folder: "Reports",
      user: "Jane Smith",
      permission: "read",
      type: "user",
    },
    {
      id: 9,
      folder: "Templates",
      user: "Design Group",
      permission: "write",
      type: "group",
    },
    {
      id: 10,
      folder: "Backup",
      user: "System Admin",
      permission: "admin",
      type: "user",
    },
  ];

  // Navigation Elements
  const logoutBtn = document.getElementById("logout-admin");

  // Initialize Admin Panel
  function initializeAdminPanel() {
    renderDashboard();
    renderPermissions();
    setupEventListeners();
  }

  // Dashboard Rendering
  function renderDashboard() {
    // Dashboard is static HTML, no dynamic rendering needed for now
    // Could add real-time stats updates here
  }

  // Permissions Management
  function renderPermissions() {
    const tableBody = document.getElementById("permissions-table-body");
    if (!tableBody) return;

    tableBody.innerHTML = permissions
      .map(
        (perm) => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <i class="fas fa-folder text-blue-500 mr-2"></i>
            <span class="text-sm font-medium text-gray-900">${
              perm.folder
            }</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <i class="fas ${
              perm.type === "user" ? "fa-user" : "fa-users"
            } text-gray-400 mr-2"></i>
            <span class="text-sm text-gray-900">${perm.user}</span>
            <span class="ml-2 text-xs text-gray-500">(${perm.type})</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="permission-badge permission-${perm.permission}">
            <i class="fas ${getPermissionIcon(perm.permission)} mr-1"></i>
            ${
              perm.permission.charAt(0).toUpperCase() + perm.permission.slice(1)
            }
          </span>
        </td>

        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button onclick="editPermission(${perm.id})"
                  class="text-blue-600 hover:text-blue-900 mr-3 p-1 rounded hover:bg-blue-50"
                  title="Edit Permission">
            <i class="fas fa-edit"></i>
          </button>
          <button onclick="duplicatePermission(${perm.id})"
                  class="text-green-600 hover:text-green-900 mr-3 p-1 rounded hover:bg-green-50"
                  title="Duplicate Permission">
            <i class="fas fa-copy"></i>
          </button>
          <button onclick="deletePermission(${perm.id})"
                  class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                  title="Delete Permission">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      </tr>
    `
      )
      .join("");
  }

  function getPermissionIcon(permission) {
    switch (permission) {
      case "read":
        return "fa-eye";
      case "write":
        return "fa-edit";
      case "admin":
        return "fa-crown";
      case "none":
        return "fa-ban";
      default:
        return "fa-question";
    }
  }

  // User Management
  function renderUsers() {
    const tableBody = document.getElementById("users-table-body");
    if (!tableBody) return;

    tableBody.innerHTML = users
      .map(
        (user) => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
              <i class="fas fa-user text-gray-600"></i>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">${user.name}</div>
              <div class="text-sm text-gray-500">${user.email}</div>
            </div>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="permission-badge ${getRoleBadgeClass(user.role)}">
            ${user.role.charAt(0).toUpperCase() + user.role.slice(1)}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="permission-badge ${
            user.status === "active" ? "permission-read" : "permission-none"
          }">
            ${user.status.charAt(0).toUpperCase() + user.status.slice(1)}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button onclick="editUser(${
            user.id
          })" class="text-blue-600 hover:text-blue-900 mr-3">
            <i class="fas fa-edit"></i>
          </button>
          <button onclick="deleteUser(${
            user.id
          })" class="text-red-600 hover:text-red-900">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      </tr>
    `
      )
      .join("");
  }

  function getRoleBadgeClass(role) {
    switch (role) {
      case "admin":
        return "permission-admin";
      case "moderator":
        return "permission-write";
      case "user":
        return "permission-read";
      default:
        return "permission-none";
    }
  }

  // Event Listeners
  function setupEventListeners() {
    // Logout functionality
    if (logoutBtn) {
      logoutBtn.addEventListener("click", () => {
        if (confirm("Are you sure you want to logout?")) {
          window.location.href = "login.html";
        }
      });
    }

    // Add Permission Button
    const addPermissionBtn = document.getElementById("add-permission-btn");
    if (addPermissionBtn) {
      addPermissionBtn.addEventListener("click", showAddPermissionModal);
    }

    // Search functionality for permissions
    const permissionSearch = document.getElementById("permission-search");
    if (permissionSearch) {
      permissionSearch.addEventListener("input", (e) => {
        filterPermissions(e.target.value);
      });
    }
  }

  function filterPermissions(searchTerm) {
    const rows = document.querySelectorAll("#permissions-table-body tr");
    rows.forEach((row) => {
      const text = row.textContent.toLowerCase();
      const matches = text.includes(searchTerm.toLowerCase());
      row.style.display = matches ? "" : "none";
    });
  }

  // Permission Management Functions
  window.editPermission = function (id) {
    const permission = permissions.find((p) => p.id === id);
    if (permission) {
      showEditPermissionModal(permission);
    }
  };

  window.deletePermission = function (id) {
    if (confirm("Are you sure you want to delete this permission?")) {
      permissions = permissions.filter((p) => p.id !== id);
      renderPermissions();
      showNotification("Permission deleted successfully!", "success");
    }
  };

  window.duplicatePermission = function (id) {
    const permission = permissions.find((p) => p.id === id);
    if (permission) {
      const newPermission = {
        ...permission,
        id: Math.max(...permissions.map((p) => p.id)) + 1,
        user: permission.user + " (Copy)",
      };
      permissions.push(newPermission);
      renderPermissions();
      showNotification("Permission duplicated successfully!", "success");
    }
  };

  // User Management Functions
  window.editUser = function (id) {
    const user = users.find((u) => u.id === id);
    if (user) {
      showEditUserModal(user);
    }
  };

  window.deleteUser = function (id) {
    if (confirm("Are you sure you want to delete this user?")) {
      users = users.filter((u) => u.id !== id);
      renderUsers();
    }
  };

  // Modal Functions - Permission CRUD
  function showAddPermissionModal() {
    const modal = createPermissionModal("Add Permission", null);
    document.body.appendChild(modal);
    modal.style.display = "flex";
  }

  function showEditPermissionModal(permission) {
    const modal = createPermissionModal("Edit Permission", permission);
    document.body.appendChild(modal);
    modal.style.display = "flex";
  }

  function createPermissionModal(title, permission = null) {
    const modal = document.createElement("div");
    modal.className =
      "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
    modal.innerHTML = `
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-800">${title}</h3>
          <button onclick="closeModal(this)" class="text-gray-400 hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <form id="permission-form" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Folder Path</label>
            <select id="folder-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
              <option value="">Select Folder</option>
              <option value="Documents">Documents</option>
              <option value="Documents/Confidential">Documents/Confidential</option>
              <option value="Documents/Public">Documents/Public</option>
              <option value="Images">Images</option>
              <option value="Images/Gallery">Images/Gallery</option>
              <option value="Videos">Videos</option>
              <option value="Archive">Archive</option>
              <option value="Shared">Shared</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">User/Group</label>
            <div class="flex space-x-2">
              <select id="user-type-select" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                <option value="user">User</option>
                <option value="group">Group</option>
              </select>
              <select id="user-select" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                <option value="">Select User/Group</option>
                <option value="John Doe">John Doe</option>
                <option value="Jane Smith">Jane Smith</option>
                <option value="Bob Johnson">Bob Johnson</option>
                <option value="Alice Brown">Alice Brown</option>
                <option value="Admin Group">Admin Group</option>
                <option value="Editor Group">Editor Group</option>
                <option value="Viewer Group">Viewer Group</option>
                <option value="All Users">All Users</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Permission Level</label>
            <select id="permission-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
              <option value="none">None (No Access)</option>
              <option value="read">Read (View Only)</option>
              <option value="write">Write (Edit & Upload)</option>
              <option value="admin">Admin (Full Control)</option>
            </select>
          </div>



          <div class="flex justify-end space-x-3 pt-4">
            <button type="button" onclick="closeModal(this)" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
              ${permission ? "Update" : "Add"} Permission
            </button>
          </div>
        </form>
      </div>
    `;

    // Pre-fill form if editing
    if (permission) {
      setTimeout(() => {
        document.getElementById("folder-select").value = permission.folder;
        document.getElementById("user-select").value = permission.user;
        document.getElementById("user-type-select").value = permission.type;
        document.getElementById("permission-select").value =
          permission.permission;
      }, 0);
    }

    // Handle form submission
    const form = modal.querySelector("#permission-form");
    form.addEventListener("submit", (e) => {
      e.preventDefault();
      if (permission) {
        updatePermission(permission.id, modal);
      } else {
        addPermission(modal);
      }
    });

    return modal;
  }

  // Permission CRUD Functions
  function addPermission(modal) {
    const folderPath = document.getElementById("folder-select").value;
    const userType = document.getElementById("user-type-select").value;
    const userName = document.getElementById("user-select").value;
    const permissionLevel = document.getElementById("permission-select").value;
    // Validation
    if (!folderPath || !userName || !permissionLevel) {
      alert("Please fill in all required fields");
      return;
    }

    // Check for duplicate permissions
    const duplicate = permissions.find(
      (p) => p.folder === folderPath && p.user === userName
    );

    if (duplicate) {
      alert("Permission already exists for this user/folder combination");
      return;
    }

    // Create new permission
    const newPermission = {
      id: Math.max(...permissions.map((p) => p.id)) + 1,
      folder: folderPath,
      user: userName,
      permission: permissionLevel,
      type: userType,
    };

    permissions.push(newPermission);
    renderPermissions();
    closeModal(modal.querySelector("button"));

    // Show success message
    showNotification("Permission added successfully!", "success");
  }

  function updatePermission(id, modal) {
    const folderPath = document.getElementById("folder-select").value;
    const userType = document.getElementById("user-type-select").value;
    const userName = document.getElementById("user-select").value;
    const permissionLevel = document.getElementById("permission-select").value;
    // Validation
    if (!folderPath || !userName || !permissionLevel) {
      alert("Please fill in all required fields");
      return;
    }

    // Find and update permission
    const permissionIndex = permissions.findIndex((p) => p.id === id);
    if (permissionIndex !== -1) {
      permissions[permissionIndex] = {
        ...permissions[permissionIndex],
        folder: folderPath,
        user: userName,
        permission: permissionLevel,
        type: userType,
      };

      renderPermissions();
      closeModal(modal.querySelector("button"));

      // Show success message
      showNotification("Permission updated successfully!", "success");
    }
  }

  // Modal utility functions
  window.closeModal = function (button) {
    const modal = button.closest(".fixed");
    if (modal) {
      modal.remove();
    }
  };

  function showNotification(message, type = "info") {
    const notification = document.createElement("div");
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
      type === "success"
        ? "bg-green-500"
        : type === "error"
        ? "bg-red-500"
        : "bg-blue-500"
    }`;
    notification.innerHTML = `
      <div class="flex items-center">
        <i class="fas ${
          type === "success"
            ? "fa-check-circle"
            : type === "error"
            ? "fa-exclamation-circle"
            : "fa-info-circle"
        } mr-2"></i>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  function showEditUserModal(user) {
    // Implementation for edit user modal
    // TODO: Implement user editing modal
  }

  // Initialize the admin panel
  initializeAdminPanel();
});
