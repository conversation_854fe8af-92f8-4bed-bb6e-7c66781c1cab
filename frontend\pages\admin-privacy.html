<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS - Privacy Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .nav-active {
            background: rgba(255, 255, 255, 0.2);
            border-left: 4px solid white;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #3b82f6;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-50">
        <!-- Admin Sidebar -->
        <div class="w-64 admin-sidebar text-white shadow-xl">
            <div class="p-6 border-b border-white border-opacity-20">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-white text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold">Admin Panel</h1>
                </div>
            </div>
            
            <nav class="p-4 space-y-2">
                <a href="admin-dashboard.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-permissions.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-key mr-3"></i>
                    <span>Permissions</span>
                </a>
                <a href="admin-users.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-users mr-3"></i>
                    <span>User Management</span>
                </a>
                <a href="admin-privacy.html" class="nav-active flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-eye-slash mr-3"></i>
                    <span>Privacy Settings</span>
                </a>
                <a href="admin-audit.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-clipboard-list mr-3"></i>
                    <span>Audit Logs</span>
                </a>
                <a href="admin-settings.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-cog mr-3"></i>
                    <span>System Settings</span>
                </a>
            </nav>
            
            <div class="absolute bottom-4 left-4 right-4">
                <a href="index.html" class="flex items-center p-3 rounded-lg bg-white bg-opacity-10 hover:bg-opacity-20 transition-colors">
                    <i class="fas fa-arrow-left mr-3"></i>
                    <span>Back to File Manager</span>
                </a>
            </div>
        </div>

        <!-- Main Admin Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Admin Header -->
            <header class="bg-white shadow-lg border-b border-gray-200 p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <h2 class="text-2xl font-bold text-gray-800">Privacy & Security Settings</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-user-circle"></i>
                        <span>Admin User</span>
                    </div>
                    <button onclick="window.location.href='login.html'" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </button>
                </div>
            </header>

            <!-- Privacy Settings Content -->
            <main class="flex-1 p-6 overflow-y-auto bg-gray-50">
                <!-- General Privacy Settings -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">General Privacy Settings</h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Default Folder Visibility</h4>
                                <p class="text-sm text-gray-600">Set default visibility for new folders</p>
                            </div>
                            <select id="default-visibility" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                <option value="private" selected>Private</option>
                                <option value="public">Public</option>
                                <option value="restricted">Restricted</option>
                            </select>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Allow Public Sharing</h4>
                                <p class="text-sm text-gray-600">Enable users to create public share links</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="allow-public-sharing" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Require Password for Sharing</h4>
                                <p class="text-sm text-gray-600">Force password protection on shared links</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="require-password">
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Allow Anonymous Access</h4>
                                <p class="text-sm text-gray-600">Allow access without user authentication</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="allow-anonymous">
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Max Share Duration (Days)</h4>
                                <p class="text-sm text-gray-600">Maximum time a share link remains active</p>
                            </div>
                            <input type="number" id="max-share-duration" value="30" min="1" max="365" class="px-3 py-2 border border-gray-300 rounded-md w-20 focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                
                <!-- Data Protection Settings -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Data Protection & Retention</h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Enable Audit Logging</h4>
                                <p class="text-sm text-gray-600">Track all user actions and file access</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable-audit" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Data Retention Period (Days)</h4>
                                <p class="text-sm text-gray-600">How long to keep deleted files in trash</p>
                            </div>
                            <input type="number" id="retention-days" value="365" min="1" max="3650" class="px-3 py-2 border border-gray-300 rounded-md w-20 focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Auto-Delete Expired Shares</h4>
                                <p class="text-sm text-gray-600">Automatically remove expired share links</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="auto-delete-shares" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Encrypt Files at Rest</h4>
                                <p class="text-sm text-gray-600">Enable encryption for stored files</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="encrypt-files" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Access Control Settings -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Access Control</h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">IP Whitelist Mode</h4>
                                <p class="text-sm text-gray-600">Only allow access from specified IP addresses</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="ip-whitelist">
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Two-Factor Authentication</h4>
                                <p class="text-sm text-gray-600">Require 2FA for all user accounts</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="require-2fa">
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Session Timeout (Minutes)</h4>
                                <p class="text-sm text-gray-600">Automatically log out inactive users</p>
                            </div>
                            <input type="number" id="session-timeout" value="30" min="5" max="480" class="px-3 py-2 border border-gray-300 rounded-md w-20 focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Max Failed Login Attempts</h4>
                                <p class="text-sm text-gray-600">Lock account after failed attempts</p>
                            </div>
                            <input type="number" id="max-login-attempts" value="5" min="3" max="10" class="px-3 py-2 border border-gray-300 rounded-md w-20 focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                
                <!-- Compliance Settings -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Compliance & Legal</h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">GDPR Compliance Mode</h4>
                                <p class="text-sm text-gray-600">Enable GDPR data protection features</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="gdpr-compliance" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Data Export Requests</h4>
                                <p class="text-sm text-gray-600">Allow users to request their data</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="data-export" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Right to be Forgotten</h4>
                                <p class="text-sm text-gray-600">Allow users to request data deletion</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="right-forgotten" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Legal Hold</h4>
                                <p class="text-sm text-gray-600">Prevent deletion of files under legal hold</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="legal-hold">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Save Settings -->
                <div class="flex justify-end space-x-4">
                    <button id="reset-settings" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        <i class="fas fa-undo mr-2"></i>
                        Reset to Defaults
                    </button>
                    <button id="save-privacy-settings" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Save Settings
                    </button>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Save settings functionality
        document.getElementById('save-privacy-settings').addEventListener('click', () => {
            // Collect all settings
            const settings = {
                defaultVisibility: document.getElementById('default-visibility').value,
                allowPublicSharing: document.getElementById('allow-public-sharing').checked,
                requirePassword: document.getElementById('require-password').checked,
                allowAnonymous: document.getElementById('allow-anonymous').checked,
                maxShareDuration: document.getElementById('max-share-duration').value,
                enableAudit: document.getElementById('enable-audit').checked,
                retentionDays: document.getElementById('retention-days').value,
                autoDeleteShares: document.getElementById('auto-delete-shares').checked,
                encryptFiles: document.getElementById('encrypt-files').checked,
                ipWhitelist: document.getElementById('ip-whitelist').checked,
                require2FA: document.getElementById('require-2fa').checked,
                sessionTimeout: document.getElementById('session-timeout').value,
                maxLoginAttempts: document.getElementById('max-login-attempts').value,
                gdprCompliance: document.getElementById('gdpr-compliance').checked,
                dataExport: document.getElementById('data-export').checked,
                rightForgotten: document.getElementById('right-forgotten').checked,
                legalHold: document.getElementById('legal-hold').checked
            };
            
            console.log('Saving privacy settings:', settings);
            alert('Privacy settings saved successfully!');
        });

        // Reset settings functionality
        document.getElementById('reset-settings').addEventListener('click', () => {
            if (confirm('Are you sure you want to reset all privacy settings to defaults?')) {
                // Reset all form values to defaults
                document.getElementById('default-visibility').value = 'private';
                document.getElementById('allow-public-sharing').checked = true;
                document.getElementById('require-password').checked = false;
                document.getElementById('allow-anonymous').checked = false;
                document.getElementById('max-share-duration').value = 30;
                document.getElementById('enable-audit').checked = true;
                document.getElementById('retention-days').value = 365;
                document.getElementById('auto-delete-shares').checked = true;
                document.getElementById('encrypt-files').checked = true;
                document.getElementById('ip-whitelist').checked = false;
                document.getElementById('require-2fa').checked = false;
                document.getElementById('session-timeout').value = 30;
                document.getElementById('max-login-attempts').value = 5;
                document.getElementById('gdpr-compliance').checked = true;
                document.getElementById('data-export').checked = true;
                document.getElementById('right-forgotten').checked = true;
                document.getElementById('legal-hold').checked = false;
                
                alert('Settings reset to defaults!');
            }
        });
    </script>
</body>
</html>
