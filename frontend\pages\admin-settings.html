<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS - System Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .nav-active {
            background: rgba(255, 255, 255, 0.2);
            border-left: 4px solid white;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #3b82f6;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-50">
        <!-- Admin Sidebar -->
        <div class="w-64 admin-sidebar text-white shadow-xl">
            <div class="p-6 border-b border-white border-opacity-20">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-white text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold">Admin Panel</h1>
                </div>
            </div>
            
            <nav class="p-4 space-y-2">
                <a href="admin-dashboard.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-permissions.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-key mr-3"></i>
                    <span>Permissions</span>
                </a>
                <a href="admin-users.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-users mr-3"></i>
                    <span>User Management</span>
                </a>
                <a href="admin-privacy.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-eye-slash mr-3"></i>
                    <span>Privacy Settings</span>
                </a>
                <a href="admin-audit.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-clipboard-list mr-3"></i>
                    <span>Audit Logs</span>
                </a>
                <a href="admin-settings.html" class="nav-active flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-cog mr-3"></i>
                    <span>System Settings</span>
                </a>
            </nav>
            
            <div class="absolute bottom-4 left-4 right-4">
                <a href="index.html" class="flex items-center p-3 rounded-lg bg-white bg-opacity-10 hover:bg-opacity-20 transition-colors">
                    <i class="fas fa-arrow-left mr-3"></i>
                    <span>Back to File Manager</span>
                </a>
            </div>
        </div>

        <!-- Main Admin Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Admin Header -->
            <header class="bg-white shadow-lg border-b border-gray-200 p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <h2 class="text-2xl font-bold text-gray-800">System Settings</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-user-circle"></i>
                        <span>Admin User</span>
                    </div>
                    <button onclick="window.location.href='login.html'" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </button>
                </div>
            </header>

            <!-- System Settings Content -->
            <main class="flex-1 p-6 overflow-y-auto bg-gray-50">
                <!-- General System Settings -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">General System Settings</h3>
                    
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">System Name</label>
                                <input type="text" id="system-name" value="Document Management System" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">System Version</label>
                                <input type="text" id="system-version" value="1.0.0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">System Description</label>
                            <textarea id="system-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">A comprehensive document management system for organizing and sharing files securely.</textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
                                <select id="default-language" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                    <option value="en" selected>English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                    <option value="it">Italian</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Default Timezone</label>
                                <select id="default-timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                    <option value="UTC" selected>UTC</option>
                                    <option value="America/New_York">Eastern Time</option>
                                    <option value="America/Chicago">Central Time</option>
                                    <option value="America/Denver">Mountain Time</option>
                                    <option value="America/Los_Angeles">Pacific Time</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Storage Settings -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Storage & Performance</h3>
                    
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max File Size (MB)</label>
                                <input type="number" id="max-file-size" value="100" min="1" max="1000" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Storage Quota per User (GB)</label>
                                <input type="number" id="storage-quota" value="10" min="1" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Enable File Compression</h4>
                                <p class="text-sm text-gray-600">Automatically compress uploaded files to save space</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable-compression" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Enable Caching</h4>
                                <p class="text-sm text-gray-600">Cache frequently accessed files for better performance</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable-caching" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Cache Duration (Hours)</label>
                                <input type="number" id="cache-duration" value="24" min="1" max="168" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Backup Frequency</label>
                                <select id="backup-frequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                    <option value="daily" selected>Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="manual">Manual Only</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Email Settings -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Email & Notifications</h3>
                    
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Server</label>
                                <input type="text" id="smtp-server" value="smtp.gmail.com" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                                <input type="number" id="smtp-port" value="587" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                                <input type="email" id="from-email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                                <input type="text" id="from-name" value="DMS System" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Enable Email Notifications</h4>
                                <p class="text-sm text-gray-600">Send email notifications for system events</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable-email" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- System Actions -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">System Actions</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button id="restart-system" class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            <i class="fas fa-redo text-blue-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-blue-800">Restart System</span>
                        </button>
                        
                        <button id="clear-cache" class="flex flex-col items-center p-4 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors">
                            <i class="fas fa-broom text-yellow-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-yellow-800">Clear Cache</span>
                        </button>
                        
                        <button id="backup-system" class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                            <i class="fas fa-download text-green-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-green-800">Backup System</span>
                        </button>
                        
                        <button id="system-health" class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                            <i class="fas fa-heartbeat text-purple-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-purple-800">Health Check</span>
                        </button>
                    </div>
                </div>
                
                <!-- Save Settings -->
                <div class="flex justify-end space-x-4">
                    <button id="reset-system-settings" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        <i class="fas fa-undo mr-2"></i>
                        Reset to Defaults
                    </button>
                    <button id="save-system-settings" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Save Settings
                    </button>
                </div>
            </main>
        </div>
    </div>

    <script>
        // System action handlers
        document.getElementById('restart-system').addEventListener('click', () => {
            if (confirm('Are you sure you want to restart the system? This will temporarily interrupt service.')) {
                alert('System restart initiated...');
            }
        });

        document.getElementById('clear-cache').addEventListener('click', () => {
            if (confirm('Clear all cached data?')) {
                alert('Cache cleared successfully!');
            }
        });

        document.getElementById('backup-system').addEventListener('click', () => {
            alert('System backup started. You will be notified when complete.');
        });

        document.getElementById('system-health').addEventListener('click', () => {
            alert('System Health: All services running normally\nCPU: 15%\nMemory: 45%\nDisk: 60%\nNetwork: Good');
        });

        // Save settings
        document.getElementById('save-system-settings').addEventListener('click', () => {
            const settings = {
                systemName: document.getElementById('system-name').value,
                systemVersion: document.getElementById('system-version').value,
                systemDescription: document.getElementById('system-description').value,
                defaultLanguage: document.getElementById('default-language').value,
                defaultTimezone: document.getElementById('default-timezone').value,
                maxFileSize: document.getElementById('max-file-size').value,
                storageQuota: document.getElementById('storage-quota').value,
                enableCompression: document.getElementById('enable-compression').checked,
                enableCaching: document.getElementById('enable-caching').checked,
                cacheDuration: document.getElementById('cache-duration').value,
                backupFrequency: document.getElementById('backup-frequency').value,
                smtpServer: document.getElementById('smtp-server').value,
                smtpPort: document.getElementById('smtp-port').value,
                fromEmail: document.getElementById('from-email').value,
                fromName: document.getElementById('from-name').value,
                enableEmail: document.getElementById('enable-email').checked
            };
            
            console.log('Saving system settings:', settings);
            alert('System settings saved successfully!');
        });

        // Reset settings
        document.getElementById('reset-system-settings').addEventListener('click', () => {
            if (confirm('Reset all system settings to defaults?')) {
                location.reload();
            }
        });
    </script>
</body>
</html>
