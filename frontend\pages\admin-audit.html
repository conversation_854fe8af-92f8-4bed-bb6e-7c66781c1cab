<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS - Audit Logs</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .nav-active {
            background: rgba(255, 255, 255, 0.2);
            border-left: 4px solid white;
        }
        
        .action-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .action-create { background: #dcfce7; color: #166534; }
        .action-read { background: #dbeafe; color: #1e40af; }
        .action-update { background: #fef3c7; color: #92400e; }
        .action-delete { background: #fecaca; color: #991b1b; }
        .action-login { background: #f3e8ff; color: #7c3aed; }
        .action-logout { background: #f1f5f9; color: #475569; }
    </style>
</head>

<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-50">
        <!-- Admin Sidebar -->
        <div class="w-64 admin-sidebar text-white shadow-xl">
            <div class="p-6 border-b border-white border-opacity-20">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-white text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold">Admin Panel</h1>
                </div>
            </div>
            
            <nav class="p-4 space-y-2">
                <a href="admin-dashboard.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-permissions.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-key mr-3"></i>
                    <span>Permissions</span>
                </a>
                <a href="admin-users.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-users mr-3"></i>
                    <span>User Management</span>
                </a>
                <a href="admin-privacy.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-eye-slash mr-3"></i>
                    <span>Privacy Settings</span>
                </a>
                <a href="admin-audit.html" class="nav-active flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-clipboard-list mr-3"></i>
                    <span>Audit Logs</span>
                </a>
                <a href="admin-settings.html" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-cog mr-3"></i>
                    <span>System Settings</span>
                </a>
            </nav>
            
            <div class="absolute bottom-4 left-4 right-4">
                <a href="index.html" class="flex items-center p-3 rounded-lg bg-white bg-opacity-10 hover:bg-opacity-20 transition-colors">
                    <i class="fas fa-arrow-left mr-3"></i>
                    <span>Back to File Manager</span>
                </a>
            </div>
        </div>

        <!-- Main Admin Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Admin Header -->
            <header class="bg-white shadow-lg border-b border-gray-200 p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <h2 class="text-2xl font-bold text-gray-800">Audit Logs</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-user-circle"></i>
                        <span>Admin User</span>
                    </div>
                    <button onclick="window.location.href='login.html'" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </button>
                </div>
            </header>

            <!-- Audit Logs Content -->
            <main class="flex-1 p-6 overflow-y-auto bg-gray-50">
                <div class="admin-card p-6 mb-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">System Activity Logs</h3>
                        <div class="flex space-x-2">
                            <button id="export-logs" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                Export Logs
                            </button>
                            <button id="clear-logs" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-trash mr-2"></i>
                                Clear Logs
                            </button>
                        </div>
                    </div>
                    
                    <!-- Filter and Search -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div>
                            <input type="text" placeholder="Search logs..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">All Actions</option>
                                <option value="create">Create</option>
                                <option value="read">Read</option>
                                <option value="update">Update</option>
                                <option value="delete">Delete</option>
                                <option value="login">Login</option>
                                <option value="logout">Logout</option>
                            </select>
                        </div>
                        <div>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">All Users</option>
                                <option value="john.doe">John Doe</option>
                                <option value="jane.smith">Jane Smith</option>
                                <option value="bob.johnson">Bob Johnson</option>
                                <option value="alice.brown">Alice Brown</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resource</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        2024-01-15 14:32:15
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <span class="text-sm text-gray-900">John Doe</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="action-badge action-create">Create</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        /Documents/report.pdf
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        192.168.1.100
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-green-600">Success</span>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        2024-01-15 14:28:42
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <span class="text-sm text-gray-900">Jane Smith</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="action-badge action-login">Login</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        System Login
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        192.168.1.105
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-green-600">Success</span>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        2024-01-15 14:25:18
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <span class="text-sm text-gray-900">Bob Johnson</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="action-badge action-delete">Delete</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        /Images/old_photo.jpg
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        192.168.1.102
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-green-600">Success</span>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        2024-01-15 14:20:33
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <span class="text-sm text-gray-900">Alice Brown</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="action-badge action-update">Update</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        /Documents/project.docx
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        192.168.1.108
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-green-600">Success</span>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        2024-01-15 14:15:07
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <span class="text-sm text-gray-900">Unknown User</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="action-badge action-login">Login</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        Failed Login Attempt
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        192.168.1.200
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-red-600">Failed</span>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        2024-01-15 14:10:22
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <span class="text-sm text-gray-900">John Doe</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="action-badge action-read">Read</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        /Videos/presentation.mp4
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        192.168.1.100
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-green-600">Success</span>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        2024-01-15 14:05:45
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <span class="text-sm text-gray-900">Jane Smith</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="action-badge action-logout">Logout</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        System Logout
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        192.168.1.105
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-green-600">Success</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="flex items-center justify-between mt-6">
                        <div class="text-sm text-gray-700">
                            Showing <span class="font-medium">1</span> to <span class="font-medium">7</span> of <span class="font-medium">247</span> results
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                                Previous
                            </button>
                            <button class="px-3 py-2 text-sm bg-blue-500 text-white rounded-lg">1</button>
                            <button class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">2</button>
                            <button class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">3</button>
                            <button class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                                Next
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Audit Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="admin-card p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-eye text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Actions</p>
                                <p class="text-2xl font-bold text-gray-900">1,247</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-check text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Successful Actions</p>
                                <p class="text-2xl font-bold text-gray-900">1,198</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-red-100 text-red-600">
                                <i class="fas fa-times text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Failed Actions</p>
                                <p class="text-2xl font-bold text-gray-900">49</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-exclamation-triangle text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Security Alerts</p>
                                <p class="text-2xl font-bold text-gray-900">3</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Export logs functionality
        document.getElementById('export-logs').addEventListener('click', () => {
            alert('Exporting audit logs to CSV...');
            // In a real implementation, this would trigger a download
        });

        // Clear logs functionality
        document.getElementById('clear-logs').addEventListener('click', () => {
            if (confirm('Are you sure you want to clear all audit logs? This action cannot be undone.')) {
                alert('Audit logs cleared successfully!');
                // In a real implementation, this would clear the logs
            }
        });
    </script>
</body>
</html>
