<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS - Admin Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .permission-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .permission-read {
            background: #dcfce7;
            color: #166534;
        }

        .permission-write {
            background: #fef3c7;
            color: #92400e;
        }

        .permission-admin {
            background: #fecaca;
            color: #991b1b;
        }

        .permission-none {
            background: #f3f4f6;
            color: #6b7280;
        }

        .tab-active {
            background: #3b82f6;
            color: white;
        }

        .tab-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-50">
        <!-- Admin Sidebar -->
        <div class="w-64 admin-sidebar text-white shadow-xl">
            <div class="p-6 border-b border-white border-opacity-20">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-white text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold">Admin Panel</h1>
                </div>
            </div>

            <nav class="p-4 space-y-2">
                <a href="admin-dashboard.html"
                    class="admin-nav-item active flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-permissions.html"
                    class="admin-nav-item flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-key mr-3"></i>
                    <span>Permissions</span>
                </a>
                <a href="admin-users.html"
                    class="admin-nav-item flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-users mr-3"></i>
                    <span>User Management</span>
                </a>
                <a href="admin-privacy.html"
                    class="admin-nav-item flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-eye-slash mr-3"></i>
                    <span>Privacy Settings</span>
                </a>
                <a href="admin-audit.html"
                    class="admin-nav-item flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-clipboard-list mr-3"></i>
                    <span>Audit Logs</span>
                </a>
                <a href="admin-settings.html"
                    class="admin-nav-item flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                    <i class="fas fa-cog mr-3"></i>
                    <span>System Settings</span>
                </a>
            </nav>

            <div class="absolute bottom-4 left-4 right-4">
                <a href="index.html"
                    class="flex items-center p-3 rounded-lg bg-white bg-opacity-10 hover:bg-opacity-20 transition-colors">
                    <i class="fas fa-arrow-left mr-3"></i>
                    <span>Back to File Manager</span>
                </a>
            </div>
        </div>

        <!-- Main Admin Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Admin Header -->
            <header class="bg-white shadow-lg border-b border-gray-200 p-4 flex justify-between items-center">
                <div class="flex items-center">
                    <h2 id="page-title" class="text-2xl font-bold text-gray-800">Dashboard</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-user-circle"></i>
                        <span>Admin User</span>
                    </div>
                    <button id="logout-admin"
                        class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </button>
                </div>
            </header>

            <!-- Admin Main Content -->
            <main class="flex-1 p-6 overflow-y-auto bg-gray-50">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="admin-section">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Stats Cards -->
                        <div class="admin-card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <i class="fas fa-folder text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Folders</p>
                                    <p class="text-2xl font-bold text-gray-900">24</p>
                                </div>
                            </div>
                        </div>

                        <div class="admin-card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <i class="fas fa-users text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                                    <p class="text-2xl font-bold text-gray-900">12</p>
                                </div>
                            </div>
                        </div>

                        <div class="admin-card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                    <i class="fas fa-shield-alt text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Protected Folders</p>
                                    <p class="text-2xl font-bold text-gray-900">8</p>
                                </div>
                            </div>
                        </div>

                        <div class="admin-card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-red-100 text-red-600">
                                    <i class="fas fa-exclamation-triangle text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Security Alerts</p>
                                    <p class="text-2xl font-bold text-gray-900">3</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="admin-card p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-user-plus text-green-500 mr-3"></i>
                                    <div>
                                        <p class="font-medium">New user registered</p>
                                        <p class="text-sm text-gray-600"><EMAIL></p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">2 hours ago</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-folder-plus text-blue-500 mr-3"></i>
                                    <div>
                                        <p class="font-medium">Folder permissions updated</p>
                                        <p class="text-sm text-gray-600">Documents/Confidential</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">4 hours ago</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-shield-alt text-yellow-500 mr-3"></i>
                                    <div>
                                        <p class="font-medium">Security policy updated</p>
                                        <p class="text-sm text-gray-600">Password requirements changed</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">1 day ago</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions Section -->
                <div id="permissions-section" class="admin-section hidden">
                    <div class="admin-card p-6 mb-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">Folder Permissions</h3>
                            <button id="add-permission-btn"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                Add Permission
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Folder</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            User/Group</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Permission</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="permissions-table-body" class="bg-white divide-y divide-gray-200">
                                    <!-- Permissions will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="../js/admin.js"></script>
</body>

</html>